* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Tangerine', serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

/* App Bar Component */
app-bar {
  display: block;
  background: rgba(255, 255, 255, 0.95);
  padding: 24px 32px;
  border-radius: 16px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

app-bar h1 {
  color: #667eea;
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
}

app-bar p {
  color: #666;
  text-align: center;
  margin-top: 8px;
  font-size: 1.1rem;
}

/* Note Form Component */
note-form {
  display: block;
  background: rgba(255, 255, 255, 0.95);
  padding: 32px;
  border-radius: 16px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

note-form h2 {
  color: #333;
  margin-bottom: 24px;
  font-size: 1.8rem;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: #555;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 1rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  font-family: 'Tangerine', serif;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.char-count {
  text-align: right;
  font-size: 0.9rem;
  color: #888;
  margin-top: 4px;
}

.char-count.warning {
  color: #f39c12;
  font-weight: 600;
}

.char-count.error {
  color: #e74c3c;
  font-weight: 600;
}

.validation-message {
  color: #e74c3c;
  font-size: 0.9rem;
  margin-top: 4px;
  display: none;
}

.validation-message.show {
  display: block;
}

.form-group input.invalid,
.form-group textarea.invalid {
  border-color: #e74c3c;
}

.form-group input.valid,
.form-group textarea.valid {
  border-color: #27ae60;
}

button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 14px 32px;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
}

button:active {
  transform: translateY(0);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Notes List Component */
notes-list {
  display: block;
  background: rgba(255, 255, 255, 0.95);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

notes-list h2 {
  color: #333;
  margin-bottom: 24px;
  font-size: 1.8rem;
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

/* Note Item Component */
note-item {
  display: block;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 4px solid #667eea;
}

note-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

note-item h3 {
  color: #333;
  font-size: 1.4rem;
  margin-bottom: 12px;
  word-wrap: break-word;
}

note-item p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

note-item .note-date {
  color: #999;
  font-size: 0.85rem;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #eee;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state svg {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
  opacity: 0.3;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 8px;
  color: #666;
}

.empty-state p {
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 12px;
  }

  app-bar {
    padding: 20px;
  }

  app-bar h1 {
    font-size: 2rem;
  }

  app-bar p {
    font-size: 1rem;
  }

  note-form,
  notes-list {
    padding: 24px;
  }

  note-form h2,
  notes-list h2 {
    font-size: 1.5rem;
  }

  .notes-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  button {
    padding: 12px 24px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  app-bar h1 {
    font-size: 1.6rem;
  }

  note-form,
  notes-list {
    padding: 20px;
  }

  note-item {
    padding: 20px;
  }
}