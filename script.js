// Data catatan
const notesData = [
    {
        id: 'notes-jT-jjsyz61J8XKiI',
        title: 'Welcome to Notes, Dimas!',
        body: 'Welcome to Notes! This is your first note. You can archive it, delete it, or create new ones.',
        createdAt: '2022-07-28T10:03:12.594Z',
        archived: false,
    },
    {
        id: 'notes-aB-cdefg12345',
        title: 'Meeting Agenda',
        body: 'Discuss project updates and assign tasks for the upcoming week.',
        createdAt: '2022-08-05T15:30:00.000Z',
        archived: false,
    },
    {
        id: 'notes-XyZ-789012345',
        title: 'Shopping List',
        body: 'Milk, eggs, bread, fruits, and vegetables.',
        createdAt: '2022-08-10T08:45:23.120Z',
        archived: false,
    },
    {
        id: 'notes-1a-2b3c4d5e6f',
        title: 'Personal Goals',
        body: 'Read two books per month, exercise three times a week, learn a new language.',
        createdAt: '2022-08-15T18:12:55.789Z',
        archived: false,
    },
    {
        id: 'notes-LMN-456789',
        title: 'Recipe: Spaghetti Bolognese',
        body: 'Ingredients: ground beef, tomatoes, onions, garlic, pasta. Steps:...',
        createdAt: '2022-08-20T12:30:40.200Z',
        archived: false,
    },
    {
        id: 'notes-QwErTyUiOp',
        title: 'Workout Routine',
        body: 'Monday: Cardio, Tuesday: Upper body, Wednesday: Rest, Thursday: Lower body, Friday: Cardio.',
        createdAt: '2022-08-25T09:15:17.890Z',
        archived: false,
    },
    {
        id: 'notes-abcdef-987654',
        title: 'Book Recommendations',
        body: "1. 'The Alchemist' by Paulo Coelho\n2. '1984' by George Orwell\n3. 'To Kill a Mockingbird' by Harper Lee",
        createdAt: '2022-09-01T14:20:05.321Z',
        archived: false,
    },
    {
        id: 'notes-zyxwv-54321',
        title: 'Daily Reflections',
        body: 'Write down three positive things that happened today and one thing to improve tomorrow.',
        createdAt: '2022-09-07T20:40:30.150Z',
        archived: false,
    },
    {
        id: 'notes-poiuyt-987654',
        title: 'Travel Bucket List',
        body: '1. Paris, France\n2. Kyoto, Japan\n3. Santorini, Greece\n4. New York City, USA',
        createdAt: '2022-09-15T11:55:44.678Z',
        archived: false,
    },
    {
        id: 'notes-asdfgh-123456',
        title: 'Coding Projects',
        body: '1. Build a personal website\n2. Create a mobile app\n3. Contribute to an open-source project',
        createdAt: '2022-09-20T17:10:12.987Z',
        archived: false,
    },
    {
        id: 'notes-5678-abcd-efgh',
        title: 'Project Deadline',
        body: 'Complete project tasks by the deadline on October 1st.',
        createdAt: '2022-09-28T14:00:00.000Z',
        archived: false,
    },
    {
        id: 'notes-9876-wxyz-1234',
        title: 'Health Checkup',
        body: 'Schedule a routine health checkup with the doctor.',
        createdAt: '2022-10-05T09:30:45.600Z',
        archived: false,
    },
    {
        id: 'notes-qwerty-8765-4321',
        title: 'Financial Goals',
        body: '1. Create a monthly budget\n2. Save 20% of income\n3. Invest in a retirement fund.',
        createdAt: '2022-10-12T12:15:30.890Z',
        archived: false,
    },
    {
        id: 'notes-98765-54321-12345',
        title: 'Holiday Plans',
        body: 'Research and plan for the upcoming holiday destination.',
        createdAt: '2022-10-20T16:45:00.000Z',
        archived: false,
    },
    {
        id: 'notes-1234-abcd-5678',
        title: 'Language Learning',
        body: 'Practice Spanish vocabulary for 30 minutes every day.',
        createdAt: '2022-10-28T08:00:20.120Z',
        archived: false,
    },
    ];

    // Custom Element 1: App Bar
    class AppBar extends HTMLElement {
    connectedCallback() {
        this.innerHTML = `
        <h1>Personal Notes</h1>
        <p>Organize your thoughts, one note at a time</p>
        `;
    }
    }
    customElements.define('app-bar', AppBar);

    // Custom Element 2: Note Form
    class NoteForm extends HTMLElement {
    connectedCallback() {
        const maxTitleLength = this.getAttribute('max-title-length') || 50;
        
        this.innerHTML = `
        <h2>Create New Note</h2>
        <form id="noteForm">
            <div class="form-group">
            <label for="noteTitle">Title</label>
            <input 
                type="text" 
                id="noteTitle" 
                placeholder="Enter note title..." 
                maxlength="${maxTitleLength}"
                required
            >
            <div class="char-count" id="titleCount">0/${maxTitleLength}</div>
            <div class="validation-message" id="titleError">Title is required</div>
            </div>
            <div class="form-group">
            <label for="noteBody">Content</label>
            <textarea 
                id="noteBody" 
                placeholder="Write your note here..." 
                required
            ></textarea>
            <div class="validation-message" id="bodyError">Content is required</div>
            </div>
            <button type="submit">Add Note</button>
        </form>
        `;

        this.setupFormValidation(maxTitleLength);
        this.setupFormSubmit();
    }

    setupFormValidation(maxLength) {
        const titleInput = this.querySelector('#noteTitle');
        const bodyInput = this.querySelector('#noteBody');
        const titleCount = this.querySelector('#titleCount');
        const titleError = this.querySelector('#titleError');
        const bodyError = this.querySelector('#bodyError');
        const submitBtn = this.querySelector('button[type="submit"]');

        // Real-time validation untuk title
        titleInput.addEventListener('input', (e) => {
        const length = e.target.value.length;
        titleCount.textContent = `${length}/${maxLength}`;
        
        if (length > maxLength * 0.8) {
            titleCount.classList.add('warning');
        } else {
            titleCount.classList.remove('warning');
        }

        if (length === parseInt(maxLength)) {
            titleCount.classList.add('error');
        } else {
            titleCount.classList.remove('error');
        }

        if (e.target.value.trim() === '') {
            e.target.classList.remove('valid');
            e.target.classList.add('invalid');
            titleError.classList.add('show');
        } else {
            e.target.classList.remove('invalid');
            e.target.classList.add('valid');
            titleError.classList.remove('show');
        }

        this.updateSubmitButton(titleInput, bodyInput, submitBtn);
        });

        // Real-time validation untuk body
        bodyInput.addEventListener('input', (e) => {
        if (e.target.value.trim() === '') {
            e.target.classList.remove('valid');
            e.target.classList.add('invalid');
            bodyError.classList.add('show');
        } else {
            e.target.classList.remove('invalid');
            e.target.classList.add('valid');
            bodyError.classList.remove('show');
        }

        this.updateSubmitButton(titleInput, bodyInput, submitBtn);
        });
    }

    updateSubmitButton(titleInput, bodyInput, submitBtn) {
        const isTitleValid = titleInput.value.trim() !== '';
        const isBodyValid = bodyInput.value.trim() !== '';
        submitBtn.disabled = !(isTitleValid && isBodyValid);
    }

    setupFormSubmit() {
        const form = this.querySelector('#noteForm');
        form.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const title = this.querySelector('#noteTitle').value.trim();
        const body = this.querySelector('#noteBody').value.trim();

        if (title && body) {
            const newNote = {
            id: `notes-${Date.now()}`,
            title: title,
            body: body,
            createdAt: new Date().toISOString(),
            archived: false
            };

            notesData.unshift(newNote);
            
            // Reset form
            form.reset();
            this.querySelector('#titleCount').textContent = `0/${this.getAttribute('max-title-length') || 50}`;
            this.querySelector('#noteTitle').classList.remove('valid', 'invalid');
            this.querySelector('#noteBody').classList.remove('valid', 'invalid');
            this.querySelector('button[type="submit"]').disabled = true;

            // Update notes list
            document.querySelector('notes-list').render();
        }
        });
    }
    }
    customElements.define('note-form', NoteForm);

    // Custom Element 3: Notes List
    class NotesList extends HTMLElement {
    connectedCallback() {
        this.render();
    }

    render() {
        if (notesData.length === 0) {
        this.innerHTML = `
            <h2>My Notes</h2>
            <div class="empty-state">
            <svg fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
            </svg>
            <h3>No notes yet</h3>
            <p>Create your first note above to get started!</p>
            </div>
        `;
        } else {
        this.innerHTML = `
            <h2>My Notes (${notesData.length})</h2>
            <div class="notes-grid">
            ${notesData.map(note => `
                <note-item 
                note-id="${note.id}"
                note-title="${this.escapeHtml(note.title)}"
                note-body="${this.escapeHtml(note.body)}"
                note-date="${note.createdAt}"
                ></note-item>
            `).join('')}
            </div>
        `;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    }
    customElements.define('notes-list', NotesList);

    // Custom Element 4: Note Item
    class NoteItem extends HTMLElement {
    connectedCallback() {
        const title = this.getAttribute('note-title');
        const body = this.getAttribute('note-body');
        const date = this.getAttribute('note-date');

        const formattedDate = new Date(date).toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
        });

        this.innerHTML = `
        <h3>${title}</h3>
        <p>${body}</p>
        <div class="note-date"> ${formattedDate}</div>
        `;
    }
}
customElements.define('note-item', NoteItem);